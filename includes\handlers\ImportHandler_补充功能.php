<?php
/**
 * ImportHandler 功能补强建议
 * 
 * 恢复从 Import_Coordinator 缺失的关键功能
 */

class ImportHandler_Supplement {
    
    private ?\NTWP\Core\ProgressTracker $progress_tracker = null;
    private ?string $task_id = null;
    
    /**
     * 设置进度跟踪器 - 来自 Import_Coordinator
     */
    public function setProgressTracker(string $task_id, \NTWP\Core\ProgressTracker $progress_tracker): void {
        $this->task_id = $task_id;
        $this->progress_tracker = $progress_tracker;
    }
    
    /**
     * 更新页面进度 - 来自 Import_Coordinator
     */
    public function updatePageProgress(string $status, string $message = ''): bool {
        if (!$this->progress_tracker || !$this->task_id) {
            return false;
        }
        
        try {
            $current_data = $this->progress_tracker->getProgress($this->task_id);
            if (!$current_data) {
                return false;
            }
            
            $current_progress = $current_data['progress'] ?? [];
            
            $progress_data = [
                'total' => $current_progress['total'] ?? 0,
                'processed' => $current_progress['processed'] ?? 0,
                'percentage' => $current_progress['percentage'] ?? 0,
                'status' => $status,
                'message' => $message
            ];
            
            return $this->progress_tracker->updateProgress($this->task_id, $progress_data);
            
        } catch (\Exception $e) {
            Logger::error_log('更新页面进度失败: ' . $e->getMessage(), 'Progress Tracking');
            return false;
        }
    }
    
    /**
     * 最终化任务状态 - 来自 Import_Coordinator
     */
    public function finalizeTaskStatus(string $final_status, array $stats = [], string $error_message = ''): bool {
        if (!$this->progress_tracker || !$this->task_id) {
            return false;
        }
        
        try {
            $final_progress = [
                'status' => $final_status,
                'total' => $stats['total'] ?? 0,
                'percentage' => $final_status === 'completed' ? 100 : 0,
                'success' => ($stats['imported'] ?? 0) + ($stats['updated'] ?? 0) + ($stats['created'] ?? 0),
                'failed' => $stats['failed'] ?? 0,
                'timing' => [
                    'endTime' => time()
                ]
            ];
            
            if ($final_status === 'failed' && !empty($error_message)) {
                $final_progress['error'] = $error_message;
                $final_progress['message'] = '同步失败: ' . $error_message;
            } elseif ($final_status === 'completed') {
                $final_progress['message'] = sprintf(
                    '同步完成: 总计=%d, 成功=%d, 失败=%d',
                    $final_progress['total'],
                    $final_progress['success'],
                    $final_progress['failed']
                );
            }
            
            return $this->progress_tracker->updateProgress($this->task_id, $final_progress);
            
        } catch (\Exception $e) {
            Logger::error_log('最终化任务状态失败: ' . $e->getMessage(), 'Progress Tracking');
            return false;
        }
    }
    
    /**
     * AJAX获取记录详情 - 来自 Import_Coordinator
     */
    public function ajax_get_record_details(): void {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'notion_to_wordpress_ajax')) {
            wp_die('Security check failed');
        }
        
        $page_id = sanitize_text_field($_POST['page_id'] ?? '');
        if (empty($page_id)) {
            wp_send_json_error('Missing page ID');
        }
        
        try {
            $page_data = $this->get_page_data($page_id);
            wp_send_json_success($page_data);
        } catch (\Exception $e) {
            wp_send_json_error('Failed to get page data: ' . $e->getMessage());
        }
    }
    
    /**
     * 注册AJAX处理器 - 来自 Import_Coordinator
     */
    public function register_ajax_handlers(): void {
        add_action('wp_ajax_notion_get_record_details', [$this, 'ajax_get_record_details']);
        add_action('wp_ajax_notion_import_single_page', [$this, 'ajax_import_single_page']);
        add_action('wp_ajax_notion_batch_import', [$this, 'ajax_batch_import']);
    }
    
    /**
     * 获取页面数据 - 来自 Import_Coordinator
     */
    public function get_page_data(string $page_id): array {
        if (empty($page_id)) {
            throw new \InvalidArgumentException('Page ID cannot be empty');
        }
        
        try {
            // 使用Notion API获取页面数据
            $page = $this->import_service->notion_api->get_page($page_id);
            
            if (empty($page)) {
                throw new \Exception('Page not found');
            }
            
            return [
                'id' => $page['id'] ?? '',
                'title' => $this->extract_page_title($page),
                'status' => $page['properties']['Status']['select']['name'] ?? 'Unknown',
                'created_time' => $page['created_time'] ?? '',
                'last_edited_time' => $page['last_edited_time'] ?? '',
                'url' => $page['url'] ?? '',
                'properties' => $page['properties'] ?? []
            ];
            
        } catch (\Exception $e) {
            Logger::error_log("获取页面数据失败 ({$page_id}): " . $e->getMessage(), 'Import Handler');
            throw $e;
        }
    }
    
    /**
     * 提取页面标题
     */
    private function extract_page_title(array $page): string {
        if (isset($page['properties']['title']['title'][0]['plain_text'])) {
            return $page['properties']['title']['title'][0]['plain_text'];
        }
        
        foreach ($page['properties'] as $property) {
            if (isset($property['title'][0]['plain_text'])) {
                return $property['title'][0]['plain_text'];
            }
        }
        
        return 'Untitled';
    }
}
