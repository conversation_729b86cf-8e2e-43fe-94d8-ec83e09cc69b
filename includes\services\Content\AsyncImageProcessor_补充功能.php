<?php
/**
 * 异步图片处理功能补强
 * 
 * 从 Import_Coordinator 中缺失的异步图片处理功能
 */

class AsyncImageProcessor {
    
    /**
     * 禁用异步图片模式 - 来自 Import_Coordinator
     */
    public function disable_async_image_mode(string $state_id = null): void {
        try {
            if (class_exists('\\NTWP\\Services\\Image_Processor')) {
                \NTWP\Services\Image_Processor::disable_async_image_mode($state_id);
                
                Logger::debug_log(
                    '异步图片模式已禁用' . ($state_id ? " (状态ID: {$state_id})" : ''),
                    'Async Image'
                );
            }
        } catch (\Exception $e) {
            Logger::error_log(
                '禁用异步图片模式失败: ' . $e->getMessage(),
                'Async Image'
            );
        }
    }
    
    /**
     * 处理异步图片 - 来自 Import_Coordinator
     */
    public function process_async_images(string $html, string $state_id = null): string {
        if (empty($html)) {
            return $html;
        }
        
        try {
            if (class_exists('\\NTWP\\Services\\Image_Processor')) {
                Logger::debug_log(
                    '开始处理异步图片' . ($state_id ? " (状态ID: {$state_id})" : ''),
                    'Async Image'
                );
                
                $processed_html = \NTWP\Services\Image_Processor::process_async_images(
                    $html, 
                    $state_id
                );
                
                // 记录处理统计
                $stats = \NTWP\Services\Image_Processor::get_performance_stats();
                if (!empty($stats)) {
                    Logger::debug_log(
                        sprintf(
                            '异步图片处理完成: 成功=%d, 失败=%d, 耗时=%.2fs',
                            $stats['success_count'] ?? 0,
                            $stats['error_count'] ?? 0,
                            $stats['total_time'] ?? 0
                        ),
                        'Async Image'
                    );
                }
                
                return $processed_html;
                
            } else {
                Logger::warning_log(
                    'Image_Processor类不存在，跳过异步图片处理',
                    'Async Image'
                );
                return $html;
            }
            
        } catch (\Exception $e) {
            Logger::error_log(
                '异步图片处理失败: ' . $e->getMessage(),
                'Async Image'
            );
            return $html; // 失败时返回原始HTML
        }
    }
    
    /**
     * 启用异步图片模式
     */
    public function enable_async_image_mode(string $state_id): void {
        try {
            if (class_exists('\\NTWP\\Services\\Image_Processor')) {
                \NTWP\Services\Image_Processor::enable_async_image_mode($state_id);
                
                Logger::debug_log(
                    "异步图片模式已启用 (状态ID: {$state_id})",
                    'Async Image'
                );
            }
        } catch (\Exception $e) {
            Logger::error_log(
                '启用异步图片模式失败: ' . $e->getMessage(),
                'Async Image'
            );
        }
    }
    
    /**
     * 并发图片处理 - 来自 Import_Coordinator
     */
    public function process_content_with_concurrent_optimization(array $blocks, int $optimal_concurrency = 5): string {
        Logger::debug_log('使用并发优化模式处理内容', 'Async Image');
        
        $state_id = 'page_import_' . uniqid();
        
        try {
            // 启用异步图片下载模式
            $this->enable_async_image_mode($state_id);
            
            // 转换内容为HTML
            $raw_content = '';
            if (class_exists('\\NTWP\\Services\\Content_Converter')) {
                $raw_content = \NTWP\Services\Content_Converter::convert_blocks_to_html(
                    $blocks, 
                    null, // 需要传入notion_api实例
                    $state_id
                );
            }
            
            // 处理异步图片下载并替换占位符
            $processed_content = $this->process_async_images($raw_content, $state_id);
            
            return $processed_content;
            
        } catch (\Exception $e) {
            Logger::error_log(
                '并发图片处理失败: ' . $e->getMessage(),
                'Async Image'
            );
            
            // 回退到传统模式
            return $this->process_content_traditional_mode($blocks);
            
        } finally {
            // 清理状态
            $this->disable_async_image_mode($state_id);
        }
    }
    
    /**
     * 传统模式内容处理（回退方案）
     */
    private function process_content_traditional_mode(array $blocks): string {
        Logger::debug_log('使用传统模式处理内容', 'Async Image');
        
        try {
            if (class_exists('\\NTWP\\Services\\Content_Converter')) {
                return \NTWP\Services\Content_Converter::convert_blocks_to_html($blocks);
            }
            return '';
        } catch (\Exception $e) {
            Logger::error_log(
                '传统模式内容处理失败: ' . $e->getMessage(),
                'Async Image'
            );
            return '';
        }
    }
}
