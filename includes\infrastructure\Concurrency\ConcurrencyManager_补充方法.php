<?php
/**
 * ConcurrencyManager 缺失方法补充建议
 * 
 * 需要添加到 ConcurrencyManager 类中的关键方法
 */

class ConcurrencyManager_Supplement {
    
    /**
     * 等待可用槽位 - 来自 Unified_Concurrency_Manager
     */
    public function wait_for_slot(string $type, int $max_wait_ms = 5000): bool {
        $start_time = microtime(true);
        $wait_interval = 50;
        
        while ((microtime(true) - $start_time) * 1000 < $max_wait_ms) {
            if ($this->can_start_task($type)) {
                return $this->start_task($type);
            }
            usleep($wait_interval * 1000);
        }
        return false;
    }
    
    /**
     * 批量任务管理 - 来自 Unified_Concurrency_Manager
     */
    public function manage_batch_tasks(string $type, int $task_count): array {
        $optimal_concurrency = $this->get_optimal_concurrency_for_type($type, $task_count);
        $batch_size = min($optimal_concurrency, $task_count);
        $batches = ceil($task_count / $batch_size);
        
        return [
            'optimal_concurrency' => $optimal_concurrency,
            'batch_size' => $batch_size,
            'total_batches' => $batches,
            'estimated_time' => $batches * 2,
            'recommendation' => match(true) {
                $batches > 20 => 'consider_splitting_further',
                $batches > 10 => 'consider_splitting',
                default => 'proceed'
            }
        ];
    }
    
    /**
     * 连接池健康检查 - 来自 Concurrent_Network_Manager
     */
    public function get_connection_pool_health(): array {
        $stats = $this->get_connection_pool_stats();
        
        $health = [
            'status' => 'healthy',
            'issues' => [],
            'recommendations' => []
        ];
        
        if ($stats['reuse_rate'] < 50 && $stats['total_requests'] > 10) {
            $health['status'] = 'warning';
            $health['issues'][] = '连接复用率过低 (' . $stats['reuse_rate'] . '%)';
            $health['recommendations'][] = '考虑增加连接池大小或检查Keep-Alive配置';
        }
        
        return $health;
    }
    
    /**
     * 动态网络延迟测试 - 来自 Dynamic_Concurrency_Manager
     */
    public function measure_network_latency(): int {
        $cached_latency = get_transient('ntwp_network_latency');
        if ($cached_latency !== false) {
            return intval($cached_latency);
        }
        
        $start_time = microtime(true);
        $latency = 1000;
        
        try {
            $test_url = 'https://api.notion.com/v1/users/me';
            $context = stream_context_create([
                'http' => [
                    'method' => 'HEAD',
                    'timeout' => 5,
                    'ignore_errors' => true
                ]
            ]);
            
            @file_get_contents($test_url, false, $context);
            $end_time = microtime(true);
            $latency = intval(($end_time - $start_time) * 1000);
            
        } catch (\Exception $e) {
            Logger::warning_log("网络延迟测试失败: " . $e->getMessage(), 'Concurrency Manager');
        }
        
        set_transient('ntwp_network_latency', $latency, 300);
        return $latency;
    }
}
